﻿using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Modularity;
using Volo.Abp.EventBus;
using Volo.Abp.Identity;
using Volo.Abp.OpenIddict;
using Volo.Abp.Uow;
using Volo.Abp.BackgroundJobs;

namespace Imip.IdentityServer;

[DependsOn(
    typeof(IdentityServerDomainModule),
    typeof(IdentityServerTestBaseModule),
    typeof(AbpIdentityDomainModule),
    typeof(AbpOpenIddictDomainModule),
    typeof(AbpEventBusModule),
    typeof(AbpUnitOfWorkModule),
    typeof(AbpBackgroundJobsModule)
)]
public class IdentityServerDomainTestModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddAlwaysAllowAuthorization();
    }
}
