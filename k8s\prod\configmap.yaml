﻿apiVersion: v1
kind: ConfigMap
metadata:
  name: imip-identity-config
  namespace: imip-identity-prod
data:
  ASPNETCORE_ENVIRONMENT: "Production"
  App__SelfUrl: "https://identity.imip.co.id"
  App__ClientUrl: "https://identity.imip.co.id"
  App__CorsOrigins: "https://identity.imip.co.id,http://identity.imip.co.id,https://identity.imip.co.id,http://identity.imip.co.id,http://localhost:3000,http://localhost:5000"
  App__AppName: "Imip.IdentityServer.PROD"
  AuthServer__Authority: "https://identity.imip.co.id"
  AuthServer__RequireHttpsMetadata: "true"
  Seq__ServerUrl: "http://**********:5341"
  ExternalAuth__ApiUrl: "http://***************/api/common/RequestAuthenticationToken"
  ExternalAuth__Enabled: "true"
  Redis__IsEnabled: "true"
  Redis__Configuration: "**********:6379"
  ActiveDirectory__Domain: "corp.imip.co.id"
  ActiveDirectory__LdapServer: "imaddc01.corp.imip.co.id"
  ActiveDirectory__BaseDn: "DC=corp,DC=imip,DC=co,DC=id"
  ActiveDirectory__Username: "<EMAIL>"
  ActiveDirectory__Port: "636"
  ActiveDirectory__UseSsl: "true"
  ActiveDirectory__Enabled: "true"
  ActiveDirectory__AutoLogin: "true"
  ActiveDirectory__WindowsAuthEnabled: "true"
  ActiveDirectory__WindowsAuthServiceUrl: "https://your-windows-auth-service.com"
  ActiveDirectory__WindowsAuthServiceApiKey: ""
  ActiveDirectory__WindowsAuthServiceTimeout: "30"
