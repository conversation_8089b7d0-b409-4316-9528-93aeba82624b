# GitLab CI/CD Configuration
# Using Docker 24.0.7 with <PERSON><PERSON><PERSON><PERSON> disabled to avoid compatibility issues
# All deployments target imdevapp18 node

stages:
  - build
  - migrate
  - deploy

# Global variables
variables:
  # Disable git clean to avoid permission issues
  GIT_CLEAN_FLAGS: none
  GIT_STRATEGY: fetch

  # Docker configuration for GitLab.com shared runners
  DOCKER_DRIVER: overlay2
  # DOCKER_TLS_CERTDIR is set in individual jobs

  # NuGet package caching
  NUGET_PACKAGES_DIRECTORY: ".nuget"

  # Common variables
  SEQ_SERVER_URL: "http://**********:5341"
  SEQ_API_KEY: "PWGKGQqjozYkMWa0x6lN"
  # CERT_PASSPHRASE: "c31cfec0-c4ac-4219-b65d-87e29fa8e042"
  ENCRYPTION_PASSPHRASE: "k3q5h6xbMkFjb5se"
  DEV_EXTERNAL_AUTH_URL: "http://***************/api/common/RequestAuthenticationToken"
  PROD_EXTERNAL_AUTH_URL: "http://***************/api/common/RequestAuthenticationToken"
  # Active Directory variables
  AD_DEV_PASSWORD: "Sulawesi1"
  AD_DEV_TOKEN_SECRET: "imip-identity-server-secure-token-key-dev"

  DEV_APP_NAME: "Imip.IdentityServer.DEV"
  PROD_APP_NAME: "Imip.IdentityServer.PROD"

  # Redis configuration
  DEV_REDIS_CONFIGURATION: "**********:6378"
  PROD_REDIS_CONFIGURATION: "**********:6379"

  # RabbitMQ configuration
  RABBITMQ_HOST: "**********"
  RABBITMQ_PORT: "5672"
  RABBITMQ_USERNAME: "guest"
  RABBITMQ_PASSWORD: "guest"
  RABBITMQ_EXCHANGE_NAME: "LogoutEvents"

  # Target node for deployment
  TARGET_NODE: "imdevapp18"
  PROD_HOSTNAME: "imprdapp27"

  # Certificate variables - the actual certificate will be stored in GitLab CI/CD variables
  # IDENTITY_SERVER_CERT: Base64 encoded .pfx file stored in GitLab CI/CD variables

  # Development environment variables
  DEV_DB_CONNECTION: "Server=**************;Database=identityprovider_dev;User ID=identityprovider_dev;Password=${DB_PASSWORD_DEV};TrustServerCertificate=true;Encrypt=true;MultipleActiveResultSets=false;"
  DEV_APP_URL: "https://api-identity-dev.imip.co.id"
  DEV_CLIENT_URL: "https://identity-dev.imip.co.id"
  DEV_CORS_ORIGINS: "https://identity-dev.imip.co.id"

  # Production environment variables
  PROD_DB_CONNECTION: "Server=**************;Database=identityprovider_prd;User ID=identityprovider_prd;Password=${DB_PASSWORD_PROD};TrustServerCertificate=true;Encrypt=true;MultipleActiveResultSets=false;"
  PROD_APP_URL: "https://identity.imip.co.id"
  PROD_CLIENT_URL: "https://identity.imip.co.id"
  PROD_CORS_ORIGINS: "https://identity.imip.co.id"

# Build stage for development

# Build .NET applications for development using Docker
build-dotnet-dev:
  stage: build
  tags:
    - docker-builder # Use your self-hosted runner to avoid GitLab minutes
  variables:
    BUILD_DIR: "${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}"
  cache:
    key: ${CI_COMMIT_REF_SLUG}-nuget
    paths:
      - ${NUGET_PACKAGES_DIRECTORY}
    policy: pull-push
  before_script:
    # Clean up any previous build artifacts to avoid permission issues
    - BUILD_DIR="${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}"
    - |
      if [ -d "$BUILD_DIR" ]; then
        echo "Cleaning up previous build directory..."
        # Special handling for problematic DLL files
        echo "Handling problematic DLL files..."
        find $BUILD_DIR -name "*.dll" -type f -exec chmod -f 644 {} \; 2>/dev/null || true

        # Use find with chmod to handle permissions better for all files
        find $BUILD_DIR -type f -exec chmod -f 644 {} \; 2>/dev/null || true
        find $BUILD_DIR -type d -exec chmod -f 755 {} \; 2>/dev/null || true

        # Try to remove the directory, but don't fail if it can't be fully removed
        echo "Note: Some permission errors during cleanup are expected and can be safely ignored"
        rm -rf $BUILD_DIR || echo "Warning: Could not fully remove $BUILD_DIR, continuing anyway"
      fi
    - mkdir -p $BUILD_DIR
    - mkdir -p $BUILD_DIR/web-build $BUILD_DIR/migrator-build $BUILD_DIR/docker-context
    # Create a clean copy of the repository to avoid permission issues
    - mkdir -p $BUILD_DIR/repo
    - cp -r * $BUILD_DIR/repo/ || true
  script:
    # Create NuGet packages directory if it doesn't exist
    - mkdir -p ${NUGET_PACKAGES_DIRECTORY}

    # Build Web Application using Docker with NuGet cache
    - docker run --rm -v $BUILD_DIR/repo:/src -v $BUILD_DIR/web-build:/output -v $(pwd)/${NUGET_PACKAGES_DIRECTORY}:/root/.nuget/packages mcr.microsoft.com/dotnet/sdk:9.0 /bin/bash -c "cd /src && dotnet restore --verbosity normal src/Imip.IdentityServer.Web/Imip.IdentityServer.Web.csproj && dotnet publish src/Imip.IdentityServer.Web/Imip.IdentityServer.Web.csproj -c Release -o /output"

    # Build DB Migrator using Docker with NuGet cache
    - docker run --rm -v $BUILD_DIR/repo:/src -v $BUILD_DIR/migrator-build:/output -v $(pwd)/${NUGET_PACKAGES_DIRECTORY}:/root/.nuget/packages mcr.microsoft.com/dotnet/sdk:9.0 /bin/bash -c "cd /src && dotnet restore --verbosity normal src/Imip.IdentityServer.DbMigrator/Imip.IdentityServer.DbMigrator.csproj && dotnet publish src/Imip.IdentityServer.DbMigrator/Imip.IdentityServer.DbMigrator.csproj -c Release -o /output"

    # Create a Docker build context with the published files
    - mkdir -p $BUILD_DIR/docker-context/web $BUILD_DIR/docker-context/db-migrator
    - cp -r $BUILD_DIR/web-build/* $BUILD_DIR/docker-context/web/
    - cp -r $BUILD_DIR/migrator-build/* $BUILD_DIR/docker-context/db-migrator/
    - cp $BUILD_DIR/repo/src/Imip.IdentityServer.Web/entrypoint.sh $BUILD_DIR/docker-context/web/
    - cp $BUILD_DIR/repo/src/Imip.IdentityServer.Web/Dockerfile.new $BUILD_DIR/docker-context/Dockerfile
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}/docker-context/
    expire_in: 1 hour
  only:
    - dev

# Build Docker images for development on dedicated builder
build-docker-dev:
  stage: build
  tags:
    - docker-builder # This targets your new runner on imdevapp20
  variables:
    DOCKER_DEFAULT_PLATFORM: "linux/amd64" # Explicitly set the target platform
    CLEANUP_OLDER_THAN: "168h" # Remove images older than 7 days
  before_script:
    - echo "$GITLAB_REGISTRY_TOKEN" | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
    # Cleanup old images to optimize storage
    - echo "Cleaning up old Docker images to optimize storage..."
    - docker image prune -a --filter "until=$CLEANUP_OLDER_THAN" --force || true
  script:
    # Set build directory
    - BUILD_DIR="${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}" # Was "/tmp/gitlab-build-${CI_PIPELINE_ID}"

    # Build Docker images with explicit platform using the prepared context
    - cd $BUILD_DIR/docker-context
    # Build and push web image
    - docker build --platform linux/amd64 -t $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} --build-arg PUBLISH_DIR=web .
    - docker push $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA}
    # Copy DB migrator Dockerfile and build
    - cp -f $BUILD_DIR/repo/src/Imip.IdentityServer.DbMigrator/Dockerfile.new ./Dockerfile || cp -f $CI_PROJECT_DIR/src/Imip.IdentityServer.DbMigrator/Dockerfile.new ./Dockerfile
    - docker build --platform linux/amd64 -t $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} --build-arg PUBLISH_DIR=db-migrator .
    - docker push $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA}

    # Tag with branch name for easier reference
    - docker tag $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} $CI_REGISTRY_IMAGE/web:$CI_COMMIT_REF_SLUG
    - docker push $CI_REGISTRY_IMAGE/web:$CI_COMMIT_REF_SLUG
    - docker tag $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_REF_SLUG
    - docker push $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_REF_SLUG
  after_script:
    # Clean up build directory
    - BUILD_DIR="${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}"
    - |
      if [ -d "$BUILD_DIR" ]; then
        echo "Cleaning up build directory..."
        # Special handling for problematic DLL files
        echo "Handling problematic DLL files..."
        find $BUILD_DIR -name "*.dll" -type f -exec chmod -f 644 {} \; 2>/dev/null || true

        # Use find with chmod to handle permissions better for all files
        find $BUILD_DIR -type f -exec chmod -f 644 {} \; 2>/dev/null || true
        find $BUILD_DIR -type d -exec chmod -f 755 {} \; 2>/dev/null || true

        # Try to remove the directory, but don't fail if it can't be fully removed
        echo "Note: Some permission errors during cleanup are expected and can be safely ignored"
        rm -rf $BUILD_DIR || echo "Warning: Could not fully remove $BUILD_DIR, continuing anyway"
      fi
    # Additional cleanup after build to free up space
    - echo "Removing build-specific images to free up space..."
    - docker rmi $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} || true
    - docker rmi $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} || true
    # Clean up dangling images and containers
    - docker system prune -f || true
  needs:
    - build-dotnet-dev
  only:
    - dev

# Build stage for production

# Build .NET applications for production using Docker
build-dotnet-prod:
  stage: build
  tags:
    - docker-builder-prod # Use your self-hosted runner to avoid GitLab minutes
  variables:
    BUILD_DIR: "${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}"
  cache:
    key: ${CI_COMMIT_REF_SLUG}-nuget
    paths:
      - ${NUGET_PACKAGES_DIRECTORY}
    policy: pull-push
  before_script:
    # Set umask to ensure files are created with permissive permissions
    - umask 0000

    # Set DOTNET_CLI_HOME environment variable
    - export DOTNET_CLI_HOME="/tmp/dotnet_cli_home"

    # Clean up any previous build artifacts to avoid permission issues
    - BUILD_DIR="${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}"
    - rm -rf $BUILD_DIR || true
    - mkdir -p $BUILD_DIR
    - chmod 777 $BUILD_DIR
    - mkdir -p $BUILD_DIR/web-build $BUILD_DIR/migrator-build $BUILD_DIR/docker-context
    - chmod -R 777 $BUILD_DIR/web-build $BUILD_DIR/migrator-build $BUILD_DIR/docker-context

    # Create a clean copy of the repository to avoid permission issues
    # Exclude the build directory from the copy to prevent recursive copy
    - mkdir -p $BUILD_DIR/repo
    - chmod 777 $BUILD_DIR/repo
    - find . -maxdepth 1 ! -name "build-*" ! -name "." -exec cp -r {} $BUILD_DIR/repo/ \;

    # Create dotnet CLI home directory with proper permissions
    - mkdir -p $DOTNET_CLI_HOME
    - chmod 777 $DOTNET_CLI_HOME
  script:
    # Create NuGet packages directory if it doesn't exist
    - mkdir -p ${NUGET_PACKAGES_DIRECTORY}

    # Build Web Application using Docker with NuGet cache
    - docker run --rm -v $BUILD_DIR/repo:/src -v $BUILD_DIR/web-build:/output -v $(pwd)/${NUGET_PACKAGES_DIRECTORY}:/root/.nuget/packages mcr.microsoft.com/dotnet/sdk:9.0 /bin/bash -c "cd /src && dotnet restore --verbosity normal src/Imip.IdentityServer.Web/Imip.IdentityServer.Web.csproj && dotnet publish src/Imip.IdentityServer.Web/Imip.IdentityServer.Web.csproj -c Release -o /output"

    # Build DB Migrator using Docker with NuGet cache
    - docker run --rm -v $BUILD_DIR/repo:/src -v $BUILD_DIR/migrator-build:/output -v $(pwd)/${NUGET_PACKAGES_DIRECTORY}:/root/.nuget/packages mcr.microsoft.com/dotnet/sdk:9.0 /bin/bash -c "cd /src && dotnet restore --verbosity normal src/Imip.IdentityServer.DbMigrator/Imip.IdentityServer.DbMigrator.csproj && dotnet publish src/Imip.IdentityServer.DbMigrator/Imip.IdentityServer.DbMigrator.csproj -c Release -o /output"

    # Create a Docker build context with the published files
    - mkdir -p $BUILD_DIR/docker-context/web $BUILD_DIR/docker-context/db-migrator
    - cp -r $BUILD_DIR/web-build/* $BUILD_DIR/docker-context/web/
    - cp -r $BUILD_DIR/migrator-build/* $BUILD_DIR/docker-context/db-migrator/
    - cp $BUILD_DIR/repo/src/Imip.IdentityServer.Web/entrypoint.sh $BUILD_DIR/docker-context/web/
    - cp $BUILD_DIR/repo/src/Imip.IdentityServer.Web/Dockerfile.new $BUILD_DIR/docker-context/Dockerfile
  artifacts:
    paths:
      - ${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}/docker-context/
    expire_in: 1 hour
  only:
    - main

# Build Docker images for production on dedicated builder
build-docker-prod:
  stage: build
  tags:
    - docker-builder-prod # This targets your new runner on imdevapp20
  variables:
    DOCKER_DEFAULT_PLATFORM: "linux/amd64" # Explicitly set the target platform
    CLEANUP_OLDER_THAN: "336h" # Remove images older than 14 days for production
  before_script:
    - echo "$GITLAB_REGISTRY_TOKEN" | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
    # Cleanup old images to optimize storage
    - echo "Cleaning up old Docker images to optimize storage..."
    - docker image prune -a --filter "until=${CLEANUP_OLDER_THAN}" --force || true
    - docker system prune --volumes -f || true
  script:
    # Set build directory
    # Fix 2: Use consistent build directory path
    - BUILD_DIR="${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}" # Was "/tmp/gitlab-build-${CI_PIPELINE_ID}"

    # Build Docker images with explicit platform using the prepared context
    - cd $BUILD_DIR/docker-context
    # Build and push web image
    - echo "Building web image with tag $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA}..."
    - docker build --platform linux/amd64 -t $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} --build-arg PUBLISH_DIR=web .
    - docker images | grep $CI_REGISTRY_IMAGE/web || echo "Web image not found in local registry"
    - echo "Pushing web image to registry..."
    - docker push $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} || { echo "Failed to push web image"; exit 1; }

    # Copy DB migrator Dockerfile and build
    - cp -f $BUILD_DIR/repo/src/Imip.IdentityServer.DbMigrator/Dockerfile.new ./Dockerfile || cp -f $CI_PROJECT_DIR/src/Imip.IdentityServer.DbMigrator/Dockerfile.new ./Dockerfile
    - echo "Building db-migrator image with tag $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA}..."
    - docker build --platform linux/amd64 -t $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} --build-arg PUBLISH_DIR=db-migrator .
    - docker images | grep $CI_REGISTRY_IMAGE/db-migrator || echo "DB migrator image not found in local registry"
    - echo "Pushing db-migrator image to registry..."
    - docker push $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} || { echo "Failed to push db-migrator image"; exit 1; }

    # Verify images exist in registry before tagging
    - echo "Verifying images exist in registry before tagging..."
    - docker pull $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} || { echo "Failed to pull web image from registry"; exit 1; }
    - docker pull $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} || { echo "Failed to pull db-migrator image from registry"; exit 1; }

    # Tag with branch name for easier reference
    - echo "Tagging images with branch name $CI_COMMIT_REF_SLUG..."
    - docker tag $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} $CI_REGISTRY_IMAGE/web:$CI_COMMIT_REF_SLUG
    - docker push $CI_REGISTRY_IMAGE/web:$CI_COMMIT_REF_SLUG
    - docker tag $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_REF_SLUG
    - docker push $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_REF_SLUG
  after_script:
    # Clean up build directory
    - BUILD_DIR="${CI_PROJECT_DIR}/build-${CI_PIPELINE_ID}"
    - |
      echo "Cleaning up build directory: $BUILD_DIR"
      if [ -d "$BUILD_DIR" ]; then
        echo "Setting proper permissions before cleanup..."
        # First, try to fix permissions on problematic directories
        find $BUILD_DIR -type d -name "bin" -o -name "obj" | xargs -I{} chmod -R 777 {} 2>/dev/null || true

        echo "Attempting to remove build directory without changing permissions..."
        # Try to remove without changing permissions first
        rm -rf $BUILD_DIR || {
          echo "Standard removal failed, trying with sudo..."
          # Try with sudo if available
          if command -v sudo &> /dev/null; then
            sudo chmod -R 777 $BUILD_DIR 2>/dev/null || true
            sudo rm -rf $BUILD_DIR || {
              echo "Sudo removal failed, trying with Docker..."
              # If that fails, try using Docker to clean up (runs as root)
              docker run --rm -v $BUILD_DIR:/data alpine:latest sh -c "chmod -R 777 /data && rm -rf /data/*" || {
                echo "Docker removal failed, trying with find command..."
                # If Docker fails, try using find to delete files with current permissions
                find $BUILD_DIR -type f -exec chmod 666 {} \; 2>/dev/null || true
                find $BUILD_DIR -type d -exec chmod 777 {} \; 2>/dev/null || true
                find $BUILD_DIR -type f -exec rm -f {} \; 2>/dev/null || true
                find $BUILD_DIR -type d -empty -delete 2>/dev/null || true
                # Final attempt with force
                rm -rf $BUILD_DIR 2>/dev/null || echo "Failed to remove build directory completely, some files may remain."
              }
            }
          else
            echo "Sudo not available, trying with find command..."
            # If sudo is not available, try using find to delete files with current permissions
            find $BUILD_DIR -type f -exec chmod 666 {} \; 2>/dev/null || true
            find $BUILD_DIR -type d -exec chmod 777 {} \; 2>/dev/null || true
            find $BUILD_DIR -type f -exec rm -f {} \; 2>/dev/null || true
            find $BUILD_DIR -type d -empty -delete 2>/dev/null || true
            # Final attempt with force
            rm -rf $BUILD_DIR 2>/dev/null || echo "Failed to remove build directory completely, some files may remain."
          fi
        }
      else
        echo "Build directory not found, skipping cleanup"
      fi

      # If all else fails, just ignore the errors and continue
      echo "Note: Any remaining permission errors during cleanup can be safely ignored"

    # Additional cleanup after build to free up space
    - |
      echo "Removing build-specific images to free up space..."
      echo "Checking for web image..."
      if docker images | grep -q "$CI_REGISTRY_IMAGE/web.*$CI_COMMIT_SHORT_SHA"; then
        docker rmi $CI_REGISTRY_IMAGE/web:${CI_COMMIT_SHORT_SHA} || echo "Failed to remove web image, continuing..."
      else
        echo "Web image not found locally"
      fi

    - |
      echo "Checking for db-migrator image..."
      if docker images | grep -q "$CI_REGISTRY_IMAGE/db-migrator.*$CI_COMMIT_SHORT_SHA"; then
        docker rmi $CI_REGISTRY_IMAGE/db-migrator:${CI_COMMIT_SHORT_SHA} || echo "Failed to remove db-migrator image, continuing..."
      else
        echo "DB migrator image not found locally"
      fi

    # Clean up dangling images and containers
    - |
      echo "Cleaning up dangling images and containers..."
      docker system prune -f || echo "Failed to prune Docker system, continuing..."
  needs:
    - build-dotnet-prod
  only:
    - main

# Create ConfigMap and Secrets for Development
prepare_dev_config:
  stage: migrate
  tags:
    - identityserverbackend # Use runner on K8s master node for Kubernetes operations
  environment:
    name: development
  script:
    # Check if kubectl is installed, if not, install it
    - |
      if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/v1.27.0/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
      else
        echo "kubectl already installed"
      fi
    # Create namespace if it doesn't exist
    - kubectl create namespace imip-identity-dev --dry-run=client -o yaml | kubectl apply -f -

    # Create or update ConfigMap
    - |
      cat <<EOF | kubectl apply -f -
      apiVersion: v1
      kind: ConfigMap
      metadata:
        name: imip-identity-config
        namespace: imip-identity-dev
      data:
        ASPNETCORE_ENVIRONMENT: "Development"
        App__SelfUrl: "${DEV_APP_URL}"
        App__ClientUrl: "${DEV_CLIENT_URL}"
        App__CorsOrigins: "${DEV_CORS_ORIGINS}"
        App__AppName: "${DEV_APP_NAME}"
        App__HealthCheckUrl: "/api/health/kubernetes"
        Seq__ServerUrl: "${SEQ_SERVER_URL}"
        AuthServer__Authority: "${DEV_APP_URL}"
        AuthServer__RequireHttpsMetadata: "false"
        ExternalAuth__ApiUrl: "${DEV_EXTERNAL_AUTH_URL}"
        ExternalAuth__Enabled: "true"
        AuthServer__CertificatePath: "/app/certs/identity-server.pfx"
        Redis__IsEnabled: "true"
        Redis__Configuration: "**********:6379,abortConnect=false,connectTimeout=30000,syncTimeout=30000,connectRetry=10,keepAlive=60,allowAdmin=true,responseTimeout=30000"
        RabbitMQ__Connections__Default__HostName: "${RABBITMQ_HOST}"
        RabbitMQ__Connections__Default__UserName: "${RABBITMQ_USERNAME}"
        RabbitMQ__Connections__Default__Password: "${RABBITMQ_PASSWORD}"
        RabbitMQ__Connections__Default__Port: "${RABBITMQ_PORT}"
        RabbitMQ__EventBus__ClientName: "IdentityServer-Dev"
        RabbitMQ__EventBus__ExchangeName: "${RABBITMQ_EXCHANGE_NAME}"
      EOF

    # Create or update Secrets
    - |
      cat <<EOF | kubectl apply -f -
      apiVersion: v1
      kind: Secret
      metadata:
        name: imip-identity-secrets
        namespace: imip-identity-dev
      type: Opaque
      stringData:
        ConnectionStrings__Default: "${DEV_DB_CONNECTION}"
        Seq__ApiKey: "${SEQ_API_KEY}"
        AuthServer__CertificatePassPhrase: "${CERT_PASSPHRASE}"
        StringEncryption__DefaultPassPhrase: "${ENCRYPTION_PASSPHRASE}"
        ActiveDirectory__Password: "${AD_DEV_PASSWORD}"
        ActiveDirectory__TokenSecret: "${AD_DEV_TOKEN_SECRET}"
      EOF

    # Create certificate secret
    - |
      cat <<EOF | kubectl apply -f -
      apiVersion: v1
      kind: Secret
      metadata:
        name: imip-identity-certificate
        namespace: imip-identity-dev
      type: Opaque
      data:
        identity-server.pfx: "${IDENTITY_SERVER_CERT}"
      EOF

    # Create GitLab registry credentials secret
    - |
      echo "Creating GitLab registry credentials secret..."
      kubectl create secret docker-registry gitlab-registry-credentials \
        --namespace=imip-identity-dev \
        --docker-server=registry.gitlab.com \
        --docker-username=$CI_REGISTRY_USER \
        --docker-password=$GITLAB_REGISTRY_TOKEN \
        --docker-email=$GITLAB_USER_EMAIL \
        --dry-run=client -o yaml | kubectl apply -f -
  only:
    - dev

# Run DB Migrator for Development
migrate_dev:
  stage: migrate
  tags:
    - identityserverbackend # Use runner on K8s master node for Kubernetes operations
  environment:
    name: development
  script:
    # Check if kubectl is installed, if not, install it
    - |
      if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/v1.27.0/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
      else
        echo "kubectl already installed"
      fi
    # Login to Docker registry
    - echo "$GITLAB_REGISTRY_TOKEN" | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY

    # Basic connectivity check
    - |
      echo "Checking basic connectivity to database server..."
      ping -c 2 ************** || echo "Warning: Could not ping database server"

    # Verify Docker image exists and is accessible
    - |
      echo "Verifying Docker image exists and is accessible..."
      if ! docker pull $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_SHORT_SHA; then
        echo "ERROR: Failed to pull image $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_SHORT_SHA"
        echo "This could be due to authentication issues or the image doesn't exist."
        echo "Checking if image was built correctly in previous stage..."
        docker images | grep db-migrator
        exit 1
      fi
      echo "Image verification successful!"

    # Recreate the GitLab registry credentials secret to ensure it's up to date
    - |
      echo "Recreating GitLab registry credentials secret..."
      kubectl delete secret gitlab-registry-credentials -n imip-identity-dev --ignore-not-found
      kubectl create secret docker-registry gitlab-registry-credentials \
        --namespace=imip-identity-dev \
        --docker-server=registry.gitlab.com \
        --docker-username=$CI_REGISTRY_USER \
        --docker-password=$GITLAB_REGISTRY_TOKEN \
        --docker-email=$GITLAB_USER_EMAIL

      # Verify the secret was created
      echo "Verifying secret creation:"
      kubectl get secret gitlab-registry-credentials -n imip-identity-dev

    # Clean up any previous failed migration jobs with the same commit SHA
    - |
      echo "Cleaning up any previous failed migration jobs..."
      JOB_NAME="imip-identity-db-migrator-$CI_COMMIT_SHORT_SHA"
      kubectl delete job $JOB_NAME -n imip-identity-dev --ignore-not-found
      # Wait a moment to ensure resources are released
      sleep 5

    # Apply DB Migrator job with proper variable substitution
    - sed -i "s|\${CI_COMMIT_SHA}|$CI_COMMIT_SHORT_SHA|g" k8s/dev/db-migrator-job.yaml
    - sed -i "s|\${CI_REGISTRY_IMAGE}|$CI_REGISTRY_IMAGE|g" k8s/dev/db-migrator-job.yaml
    - |
      echo "Applying DB migrator job with the following configuration:"
      cat k8s/dev/db-migrator-job.yaml
    - kubectl apply -f k8s/dev/db-migrator-job.yaml
    # Wait for the migration job to complete with reduced timeout (5 minutes)
    - |
      # Verify that we have a valid commit SHA
      if [ -z "$CI_COMMIT_SHORT_SHA" ]; then
        echo "Error: CI_COMMIT_SHORT_SHA is empty. Cannot proceed with migration job."
        exit 1
      fi

      echo "Using commit SHA: $CI_COMMIT_SHORT_SHA"
      echo "Waiting for database migration job to complete (timeout: 5 minutes)..."

      JOB_NAME="imip-identity-db-migrator-$CI_COMMIT_SHORT_SHA"
      echo "Job name: $JOB_NAME"

      # First, wait for the pod to be created and running
      echo "Waiting for pod to start..."
      RETRY_COUNT=0
      MAX_RETRIES=30
      POD_RUNNING=false

      while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        POD_NAME=$(kubectl get pods -n imip-identity-dev -l "job-name=$JOB_NAME" -o name 2>/dev/null | head -n 1)
        if [ -n "$POD_NAME" ]; then
          POD_STATUS=$(kubectl get $POD_NAME -n imip-identity-dev -o jsonpath='{.status.phase}' 2>/dev/null)
          if [ "$POD_STATUS" = "Running" ]; then
            echo "Pod $POD_NAME is running!"
            POD_RUNNING=true
            break
          elif [ "$POD_STATUS" = "Succeeded" ]; then
            echo "Pod $POD_NAME has already completed successfully!"
            POD_RUNNING=true
            break
          fi
        fi
        echo "Waiting for pod to start (attempt $((RETRY_COUNT+1))/$MAX_RETRIES)..."
        RETRY_COUNT=$((RETRY_COUNT+1))
        sleep 2
      done

      if [ "$POD_RUNNING" = "false" ]; then
        echo "Pod did not start within the expected time. Checking for errors..."
        kubectl get pods -n imip-identity-dev -l "job-name=$JOB_NAME" -o wide
        POD_NAME=$(kubectl get pods -n imip-identity-dev -l "job-name=$JOB_NAME" -o name 2>/dev/null | head -n 1)
        if [ -n "$POD_NAME" ]; then
          echo "Pod details:"
          kubectl describe $POD_NAME -n imip-identity-dev
        fi
        exit 1
      fi

      # Now wait for job completion
      if ! kubectl wait --for=condition=complete --timeout=300s "job/$JOB_NAME" -n imip-identity-dev; then
        echo "Migration job timed out or failed. Checking logs..."

        # List all pods in the namespace to help with debugging
        echo "All pods in namespace:"
        kubectl get pods -n imip-identity-dev

        # Try to find the pod with a more robust approach
        echo "Looking for pod with label job-name=$JOB_NAME"
        POD_LIST=$(kubectl get pods -n imip-identity-dev -l "job-name=$JOB_NAME" -o name)

        if [ -n "$POD_LIST" ]; then
          # Get the first pod if there are multiple
          POD_NAME=$(echo "$POD_LIST" | head -n 1 | sed 's/^pod\///')
          echo "Found pod: $POD_NAME"

          echo "Migration job pod logs:"
          kubectl logs "pod/$POD_NAME" -n imip-identity-dev || echo "Failed to get logs"

          echo "Pod details:"
          kubectl describe "pod/$POD_NAME" -n imip-identity-dev || echo "Failed to describe pod"

          # Check for specific error conditions
          POD_STATUS=$(kubectl get pod/$POD_NAME -n imip-identity-dev -o jsonpath='{.status.containerStatuses[0].state}' 2>/dev/null)
          if [[ $POD_STATUS == *"ImagePullBackOff"* ]]; then
            echo "ERROR: Image pull failure detected. This is likely due to authentication issues."
            echo "Verifying registry credentials..."
            kubectl get secret gitlab-registry-credentials -n imip-identity-dev -o yaml | grep -v "dockerconfigjson:"
          fi
        else
          echo "Could not find any pods for job: $JOB_NAME"
          echo "Checking job status:"
          kubectl describe "job/$JOB_NAME" -n imip-identity-dev || echo "Failed to describe job"
        fi
        exit 1
      else
        echo "Migration job completed successfully!"
        # Get the logs from the successful job for reference
        POD_NAME=$(kubectl get pods -n imip-identity-dev -l "job-name=$JOB_NAME" -o name | head -n 1 | sed 's/^pod\///')
        if [ -n "$POD_NAME" ]; then
          echo "Migration job logs:"
          kubectl logs "pod/$POD_NAME" -n imip-identity-dev
        fi
      fi
  needs:
    - prepare_dev_config
  only:
    - dev

# Deploy to Development
deploy_dev:
  stage: deploy
  tags:
    - identityserverbackend # Use runner on K8s master node for Kubernetes operations
  environment:
    name: development
    url: https://api-identity-dev.imip.co.id
  script:
    # Check if kubectl is installed, if not, install it
    - |
      if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/v1.27.0/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
      else
        echo "kubectl already installed"
      fi

    # Apply Pod Disruption Budget to ensure minimum availability
    - echo "Applying Pod Disruption Budget..."
    - kubectl apply -f k8s/dev/pod-disruption-budget.yaml

    # Clean up old jobs but preserve running pods for zero-downtime deployment
    - |
      echo "Cleaning up old completed jobs..."
      # List all jobs in the namespace and filter using grep/awk instead of field selectors
      OLD_JOBS=$(kubectl get jobs -n imip-identity-dev -o name | grep "imip-identity-db-migrator")
      if [ -n "$OLD_JOBS" ]; then
        echo "Found completed jobs to clean up:"
        echo "$OLD_JOBS"
        # Use a loop to handle each job individually to prevent errors if one job can't be deleted
        for JOB in $OLD_JOBS; do
          echo "Deleting job: $JOB"
          kubectl delete $JOB -n imip-identity-dev --ignore-not-found || echo "Failed to delete $JOB, continuing..."
        done
      fi

      # Check node resources
      echo "Node resource usage:"
      kubectl describe nodes k8s-worker1 | grep -A 10 "Allocated resources"

    # Recreate the GitLab registry credentials secret to ensure it's up to date
    - |
      echo "Recreating GitLab registry credentials secret for web deployment..."
      kubectl delete secret gitlab-registry-credentials -n imip-identity-dev --ignore-not-found
      kubectl create secret docker-registry gitlab-registry-credentials \
        --namespace=imip-identity-dev \
        --docker-server=registry.gitlab.com \
        --docker-username=$CI_REGISTRY_USER \
        --docker-password=$GITLAB_REGISTRY_TOKEN \
        --docker-email=$GITLAB_USER_EMAIL

      # Verify the secret was created
      echo "Verifying secret creation:"
      kubectl get secret gitlab-registry-credentials -n imip-identity-dev

    # Apply web deployment using the YAML file
    - sed -i "s|\${CI_REGISTRY_IMAGE}|$CI_REGISTRY_IMAGE|g" k8s/dev/web-deployment.yaml
    - sed -i "s|\${CI_COMMIT_SHA}|$CI_COMMIT_SHORT_SHA|g" k8s/dev/web-deployment.yaml
    - kubectl apply -f k8s/dev/web-deployment.yaml

    # Apply services
    - kubectl apply -f k8s/dev/web-service.yaml
    - kubectl apply -f k8s/dev/web-service-nodeport.yaml

    # Apply ingress
    - kubectl apply -f k8s/dev/ingress.yaml

    # Monitor deployment status with improved handling
    - |
      echo "Monitoring deployment status..."
      if kubectl rollout status deployment/imip-identity-web -n imip-identity-dev --timeout=300s; then
        echo "✅ Deployment completed successfully!"
      else
        echo "⚠️ Deployment may not have completed within the timeout period."
        echo "Checking deployment status..."
        kubectl get deployment imip-identity-web -n imip-identity-dev -o wide

        echo "Current pod status:"
        kubectl get pods -n imip-identity-dev -l app=imip-identity-web -o wide

        # Check for any problematic pods and get their logs
        PROBLEMATIC_PODS=$(kubectl get pods -n imip-identity-dev -l app=imip-identity-web --field-selector=status.phase!=Running,status.phase!=Succeeded,status.phase!=Completed -o name)
        if [ -n "$PROBLEMATIC_PODS" ]; then
          echo "Found problematic pods:"
          for POD in $PROBLEMATIC_PODS; do
            echo "Details for $POD:"
            kubectl describe $POD -n imip-identity-dev
            echo "Logs for $POD:"
            kubectl logs $POD -n imip-identity-dev --tail=50 || echo "Could not get logs"
          done
        fi

        # Check for any events that might indicate issues
        echo "Recent events:"
        kubectl get events -n imip-identity-dev --sort-by='.lastTimestamp' | tail -n 20

        # Continue anyway - we don't want to fail the pipeline if some pods are still starting
        echo "Continuing despite deployment issues..."
      fi

      # Check if we have at least one pod running (minimum availability)
      RUNNING_PODS=$(kubectl get pods -n imip-identity-dev -l app=imip-identity-web --field-selector=status.phase=Running -o name | wc -l)
      if [ "$RUNNING_PODS" -ge 1 ]; then
        echo "✅ At least one pod is running. Service should be available."
      else
        echo "⚠️ Warning: No running pods found. Service may be unavailable!"
        # We still don't fail the pipeline, but this is a clear warning
      fi
  needs:
    - migrate_dev
  only:
    - dev

# Create ConfigMap and Secrets for Production
prepare_prod_config:
  stage: migrate
  tags:
    - identityserverbackendprod
  environment:
    name: production
  script:
    # Check if kubectl is installed, if not, install it
    - |
      if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/v1.27.0/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
      else
        echo "kubectl already installed"
      fi

    # Quick API server check
    - |
      echo "Checking Kubernetes API server..."
      if ! kubectl cluster-info &>/dev/null; then
        echo "Failed to connect to Kubernetes API server"
        exit 1
      fi

    # Create namespace if it doesn't exist
    - |
      echo "Creating namespace..."
      kubectl create namespace imip-identity-prod --dry-run=client -o yaml | kubectl apply -f -

    # Apply all configurations
    - |
      echo "Applying configurations..."
      # Create PV
      envsubst < k8s/prod/data-protection-pv.yaml | kubectl apply -f -
      # Create PVC
      envsubst < k8s/prod/data-protection-pvc.yaml | kubectl apply -f -

    # Wait for PVC to bind
    - |
      echo "Waiting for PVC to bind..."
      kubectl wait --for=condition=Bound pvc/imip-identity-data-protection -n imip-identity-prod --timeout=60s || true

    # Verify status
    - |
      echo "Checking PV/PVC status:"
      kubectl get pv imip-identity-data-protection-pv-prod
      kubectl get pvc -n imip-identity-prod imip-identity-data-protection

    # Apply remaining configurations
    - |
      echo "Applying remaining configurations..."
      # Apply ConfigMap
      envsubst < k8s/prod/configmap.yaml | kubectl apply -f -
      # Apply Secrets
      envsubst < k8s/prod/secrets.yaml | kubectl apply -f -
      # Apply Certificate Secret
      envsubst < k8s/prod/certificate-secret.yaml | kubectl apply -f -

    # Create registry credentials
    - |
      echo "Creating registry credentials..."
      kubectl create secret docker-registry gitlab-registry-credentials \
        --namespace=imip-identity-prod \
        --docker-server=registry.gitlab.com \
        --docker-username=$CI_REGISTRY_USER \
        --docker-password=$GITLAB_REGISTRY_TOKEN \
        --docker-email=$GITLAB_USER_EMAIL \
        --dry-run=client -o yaml | kubectl apply -f -

    # Verify configurations
    - |
      echo "Verifying configurations..."
      kubectl get pv,pvc,configmap,secret -n imip-identity-prod
  only:
    - main

# Run DB Migrator for Production
migrate_prod:
  stage: migrate
  tags:
    - identityserverbackendprod # Use runner on K8s master node for Kubernetes operations
  environment:
    name: production
  script:
    # Check if kubectl is installed, if not, install it, and test
    - |
      if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/v1.27.0/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
      else
        echo "kubectl already installed"
      fi
    # Login to Docker registry
    - echo "$GITLAB_REGISTRY_TOKEN" | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY

    # Basic connectivity check
    - |
      echo "Checking basic connectivity to database server..."
      ping -c 2 ************** || echo "Warning: Could not ping database server"

    # Verify Docker image exists and is accessible
    - |
      echo "Verifying Docker image exists and is accessible..."
      if ! docker pull $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_SHORT_SHA; then
        echo "ERROR: Failed to pull image $CI_REGISTRY_IMAGE/db-migrator:$CI_COMMIT_SHORT_SHA"
        echo "This could be due to authentication issues or the image doesn't exist."
        echo "Checking if image was built correctly in previous stage..."
        docker images | grep db-migrator
        exit 1
      fi
      echo "Image verification successful!"

    # Recreate the GitLab registry credentials secret to ensure it's up to date
    - |
      echo "Creating registry credentials..."
      kubectl create secret docker-registry gitlab-registry-credentials \
        --namespace=imip-identity-prod \
        --docker-server=registry.gitlab.com \
        --docker-username=$CI_REGISTRY_USER \
        --docker-password=$GITLAB_REGISTRY_TOKEN \
        --docker-email=$GITLAB_USER_EMAIL \
        --dry-run=client -o yaml | kubectl apply -f -

      # Verify the secret was created
      echo "Verifying secret creation:"
      kubectl get secret gitlab-registry-credentials -n imip-identity-prod

    # Clean up any previous failed migration jobs with the same commit SHA
    - |
      echo "Cleaning up any previous failed migration jobs..."
      JOB_NAME="imip-identity-db-migrator-$CI_COMMIT_SHORT_SHA"
      kubectl delete job $JOB_NAME -n imip-identity-prod --ignore-not-found
      # Wait a moment to ensure resources are released
      sleep 5

    # Apply DB Migrator job with proper variable substitution
    - sed -i "s|\${CI_COMMIT_SHA}|$CI_COMMIT_SHORT_SHA|g" k8s/prod/db-migrator-job.yaml
    - sed -i "s|\${CI_REGISTRY_IMAGE}|$CI_REGISTRY_IMAGE|g" k8s/prod/db-migrator-job.yaml
    - |
      echo "Applying DB migrator job with the following configuration:"
      cat k8s/prod/db-migrator-job.yaml
    - kubectl apply -f k8s/prod/db-migrator-job.yaml
    # Wait for the migration job to complete with proper timeout
    - |
      # Verify that we have a valid commit SHA
      if [ -z "$CI_COMMIT_SHORT_SHA" ]; then
        echo "Error: CI_COMMIT_SHORT_SHA is empty. Cannot proceed with migration job."
        exit 1
      fi

      echo "Using commit SHA: $CI_COMMIT_SHORT_SHA"
      echo "Waiting for database migration job to complete (timeout: 10 minutes)..."

      JOB_NAME="imip-identity-db-migrator-$CI_COMMIT_SHORT_SHA"
      echo "Job name: $JOB_NAME"

      # First, wait for the pod to be created and running
      echo "Waiting for pod to start..."
      RETRY_COUNT=0
      MAX_RETRIES=30
      POD_RUNNING=false

      while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        POD_NAME=$(kubectl get pods -n imip-identity-prod -l "job-name=$JOB_NAME" -o name 2>/dev/null | head -n 1)
        if [ -n "$POD_NAME" ]; then
          POD_STATUS=$(kubectl get $POD_NAME -n imip-identity-prod -o jsonpath='{.status.phase}' 2>/dev/null)
          if [ "$POD_STATUS" = "Running" ]; then
            echo "Pod $POD_NAME is running!"
            POD_RUNNING=true
            break
          elif [ "$POD_STATUS" = "Succeeded" ]; then
            echo "Pod $POD_NAME has already completed successfully!"
            POD_RUNNING=true
            break
          fi
        fi
        echo "Waiting for pod to start (attempt $((RETRY_COUNT+1))/$MAX_RETRIES)..."
        RETRY_COUNT=$((RETRY_COUNT+1))
        sleep 2
      done

      if [ "$POD_RUNNING" = "false" ]; then
        echo "Pod did not start within the expected time. Checking for errors..."
        kubectl get pods -n imip-identity-prod -l "job-name=$JOB_NAME" -o wide
        POD_NAME=$(kubectl get pods -n imip-identity-prod -l "job-name=$JOB_NAME" -o name 2>/dev/null | head -n 1)
        if [ -n "$POD_NAME" ]; then
          echo "Pod details:"
          kubectl describe $POD_NAME -n imip-identity-prod
        fi
        exit 1
      fi

      # Now wait for job completion with proper timeout
      if ! kubectl wait --for=condition=complete --timeout=600s "job/$JOB_NAME" -n imip-identity-prod; then
        echo "Migration job timed out or failed. Checking logs..."

        # List all pods in the namespace to help with debugging
        echo "All pods in namespace:"
        kubectl get pods -n imip-identity-prod

        # Try to find the pod with a more robust approach
        echo "Looking for pod with label job-name=$JOB_NAME"
        POD_LIST=$(kubectl get pods -n imip-identity-prod -l "job-name=$JOB_NAME" -o name)

        if [ -n "$POD_LIST" ]; then
          # Get the first pod if there are multiple
          POD_NAME=$(echo "$POD_LIST" | head -n 1 | sed 's/^pod\///')
          echo "Found pod: $POD_NAME"

          echo "Migration job pod logs:"
          kubectl logs "pod/$POD_NAME" -n imip-identity-prod || echo "Failed to get logs"

          echo "Pod details:"
          kubectl describe "pod/$POD_NAME" -n imip-identity-prod || echo "Failed to describe pod"

          # Check for specific error conditions
          POD_STATUS=$(kubectl get pod/$POD_NAME -n imip-identity-prod -o jsonpath='{.status.containerStatuses[0].state}' 2>/dev/null)
          if [[ $POD_STATUS == *"ImagePullBackOff"* ]]; then
            echo "ERROR: Image pull failure detected. This is likely due to authentication issues."
            echo "Verifying registry credentials..."
            kubectl get secret gitlab-registry-credentials -n imip-identity-prod -o yaml | grep -v "dockerconfigjson:"
          fi
        else
          echo "Could not find any pods for job: $JOB_NAME"
          echo "Checking job status:"
          kubectl describe "job/$JOB_NAME" -n imip-identity-prod || echo "Failed to describe job"
        fi
        exit 1
      else
        echo "Migration job completed successfully!"
        # Get the logs from the successful job for reference
        POD_NAME=$(kubectl get pods -n imip-identity-prod -l "job-name=$JOB_NAME" -o name | head -n 1 | sed 's/^pod\///')
        if [ -n "$POD_NAME" ]; then
          echo "Migration job logs:"
          kubectl logs "pod/$POD_NAME" -n imip-identity-prod
        fi
      fi
  needs:
    - prepare_prod_config
  only:
    - main

# Deploy to Production
deploy_prod:
  stage: deploy
  tags:
    - identityserverbackendprod # Use runner on K8s master node for Kubernetes operations
  environment:
    name: production
    url: https://identity.imip.co.id
  script:
    # Check if kubectl is installed, if not, install it
    - |
      if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found, installing..."
        curl -LO "https://dl.k8s.io/release/v1.27.0/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
      else
        echo "kubectl already installed"
      fi

    # Check ingress-nginx webhook status
    - |
      echo "Checking ingress-nginx webhook status..."
      if ! kubectl get validatingwebhookconfigurations ingress-nginx-admission &>/dev/null; then
        echo "Warning: ingress-nginx webhook not found, attempting to fix..."
        # Try to patch the webhook to add a timeout
        kubectl patch validatingwebhookconfigurations ingress-nginx-admission --type='json' -p='[{"op": "replace", "path": "/webhooks/0/timeoutSeconds", "value": 30}]' || true
      fi

    # Basic connectivity check
    - |
      echo "Checking basic connectivity to database server..."
      ping -c 2 ************** || echo "Warning: Could not ping database server"

    # Apply Pod Disruption Budget to ensure minimum availability
    - echo "Applying Pod Disruption Budget..."
    - kubectl apply -f k8s/prod/pod-disruption-budget.yaml

    # Clean up old jobs but preserve running pods for zero-downtime deployment
    - |
      echo "Cleaning up old completed jobs..."
      # List all jobs in the namespace and filter using grep/awk instead of field selectors
      OLD_JOBS=$(kubectl get jobs -n imip-identity-prod -o name | grep "imip-identity-db-migrator")
      if [ -n "$OLD_JOBS" ]; then
        echo "Found completed jobs to clean up:"
        echo "$OLD_JOBS"
        # Use a loop to handle each job individually to prevent errors if one job can't be deleted
        for JOB in $OLD_JOBS; do
          echo "Deleting job: $JOB"
          kubectl delete $JOB -n imip-identity-prod --ignore-not-found || echo "Failed to delete $JOB, continuing..."
        done
      fi

      # Check node resources
      echo "Node resource usage:"
      kubectl describe nodes ${PROD_HOSTNAME} | grep -A 10 "Allocated resources"

    # Recreate the GitLab registry credentials secret to ensure it's up to date
    - |
      echo "Creating registry credentials..."
      kubectl create secret docker-registry gitlab-registry-credentials \
        --namespace=imip-identity-prod \
        --docker-server=registry.gitlab.com \
        --docker-username=$CI_REGISTRY_USER \
        --docker-password=$GITLAB_REGISTRY_TOKEN \
        --docker-email=$GITLAB_USER_EMAIL \
        --dry-run=client -o yaml | kubectl apply -f -

      # Verify the secret was created
      echo "Verifying secret creation:"
      kubectl get secret gitlab-registry-credentials -n imip-identity-prod

    # Apply web deployment using the YAML file
    - sed -i "s|\${CI_REGISTRY_IMAGE}|$CI_REGISTRY_IMAGE|g" k8s/prod/web-deployment.yaml
    - sed -i "s|\${CI_COMMIT_SHA}|$CI_COMMIT_SHORT_SHA|g" k8s/prod/web-deployment.yaml
    - echo "Applying web deployment with environment variable substitution..."
    - envsubst < k8s/prod/web-deployment.yaml | kubectl apply -f -

    # Apply services
    - kubectl apply -f k8s/prod/web-service.yaml
    # Apply NodePort service for direct IP access
    - echo "Applying NodePort service for direct IP access..."
    - kubectl apply -f k8s/prod/web-service-nodeport.yaml

    # Apply ingress with retry mechanism and health check
    - |
      echo "Applying ingress configuration..."
      MAX_RETRIES=3
      RETRY_COUNT=0

      # Clean up existing webhook configurations
      echo "Cleaning up existing webhook configurations..."
      kubectl delete validatingwebhookconfigurations ingress-nginx-admission --ignore-not-found
      kubectl delete job -n ingress-nginx ingress-nginx-admission-create ingress-nginx-admission-patch --ignore-not-found
      sleep 5  # Wait for cleanup to complete

      # First, ensure ingress-nginx controller is healthy
      echo "Checking ingress-nginx controller health..."
      if ! kubectl get pods -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx | grep -q "Running"; then
        echo "Warning: ingress-nginx controller not running properly"
        kubectl describe pods -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx
        echo "Attempting to fix ingress-nginx controller..."
        kubectl rollout restart deployment -n ingress-nginx ingress-nginx-controller
        echo "Waiting for ingress-nginx controller to be ready..."
        kubectl rollout status deployment -n ingress-nginx ingress-nginx-controller --timeout=300s
      fi

      # Wait for ingress-nginx controller to be fully ready
      echo "Waiting for ingress-nginx controller to be fully ready..."
      kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=ingress-nginx -n ingress-nginx --timeout=300s

      # Now try to apply the ingress
      while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        if kubectl apply -f k8s/prod/ingress.yaml; then
          echo "Ingress applied successfully!"
          # Verify the ingress was created
          if kubectl get ingress -n imip-identity-prod imip-identity-web; then
            echo "Ingress verification successful!"
            # Wait for ingress to be ready
            echo "Waiting for ingress to be ready..."
            sleep 10
            # Check ingress status
            kubectl describe ingress -n imip-identity-prod imip-identity-web
            break
          else
            echo "Ingress was not created properly, retrying..."
          fi
        else
          RETRY_COUNT=$((RETRY_COUNT+1))
          if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
            echo "Failed to apply ingress after $MAX_RETRIES attempts"
            echo "Checking ingress-nginx controller status:"
            kubectl get pods -n ingress-nginx
            kubectl describe pods -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx
            kubectl get events -n ingress-nginx
            exit 1
          fi
          echo "Retrying ingress application (attempt $RETRY_COUNT of $MAX_RETRIES)..."
          sleep 10
        fi
      done

      # Verify ingress is working
      echo "Verifying ingress is working..."
      # Get the ingress controller's external IP
      INGRESS_IP=$(kubectl get service -n ingress-nginx ingress-nginx-controller -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
      if [ -z "$INGRESS_IP" ]; then
        echo "Warning: Could not get ingress controller IP"
        INGRESS_IP=$(kubectl get nodes -o wide | awk 'NR==2 {print $6}')
      fi
      echo "Ingress controller IP: $INGRESS_IP"

      # Test the ingress
      echo "Testing ingress access..."
      curl -v -H "Host: identity.imip.co.id" http://$INGRESS_IP || echo "Ingress test completed"

    # Monitor deployment status with improved handling
    - |
      echo "Monitoring deployment status..."
      if kubectl rollout status deployment/imip-identity-web -n imip-identity-prod --timeout=300s; then
        echo "✅ Deployment completed successfully!"
      else
        echo "⚠️ Deployment may not have completed within the timeout period."
        echo "Checking deployment status..."
        kubectl get deployment imip-identity-web -n imip-identity-prod -o wide

        echo "Current pod status:"
        kubectl get pods -n imip-identity-prod -l app=imip-identity-web -o wide

        # Check for any problematic pods and get their logs
        PROBLEMATIC_PODS=$(kubectl get pods -n imip-identity-prod -l app=imip-identity-web --field-selector=status.phase!=Running,status.phase!=Succeeded,status.phase!=Completed -o name)
        if [ -n "$PROBLEMATIC_PODS" ]; then
          echo "Found problematic pods:"
          for POD in $PROBLEMATIC_PODS; do
            echo "Details for $POD:"
            kubectl describe $POD -n imip-identity-prod
            echo "Logs for $POD:"
            kubectl logs $POD -n imip-identity-prod --tail=50 || echo "Could not get logs"
          done
        fi

        # Check for any events that might indicate issues
        echo "Recent events:"
        kubectl get events -n imip-identity-prod --sort-by='.lastTimestamp' | tail -n 20

        # Continue anyway - we don't want to fail the pipeline if some pods are still starting
        echo "Continuing despite deployment issues..."
      fi

      # Check if we have at least one pod running (minimum availability)
      RUNNING_PODS=$(kubectl get pods -n imip-identity-prod -l app=imip-identity-web --field-selector=status.phase=Running -o name | wc -l)
      if [ "$RUNNING_PODS" -ge 1 ]; then
        echo "✅ At least one pod is running. Service should be available."
      else
        echo "⚠️ Warning: No running pods found. Service may be unavailable!"
        # We still don't fail the pipeline, but this is a clear warning
      fi

  needs:
    - migrate_prod
  only:
    - main
