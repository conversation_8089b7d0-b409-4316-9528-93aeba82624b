﻿apiVersion: v1
kind: ConfigMap
metadata:
  name: imip-identity-config
  namespace: imip-identity-dev
data:
  ASPNETCORE_ENVIRONMENT: "Development"
  App__SelfUrl: "https://api-identity-dev.imip.co.id"
  App__ClientUrl: "https://identity-dev.imip.co.id"
  App__CorsOrigins: "https://identity-dev.imip.co.id,http://identity-dev.imip.co.id,https://api-identity-dev.imip.co.id,http://api-identity-dev.imip.co.id,https://localhost:44309,http://localhost:3000"
  App__AppName: "Imip.IdentityServer.DEV"
  App__HealthCheckUrl: "/api/health/kubernetes"
  AuthServer__Authority: "https://api-identity-dev.imip.co.id"
  AuthServer__RequireHttpsMetadata: "false"
  AuthServer__CertificatePath: "/app/certs/identity-server.pfx"
  Seq__ServerUrl: "http://**********:5341"
  ExternalAuth__ApiUrl: "http://***************/api/common/RequestAuthenticationToken"
  ExternalAuth__Enabled: "true"
  Redis__IsEnabled: "true"
  ActiveDirectory__Domain: "corp.imip.co.id"
  ActiveDirectory__LdapServer: "imaddc01.corp.imip.co.id"
  ActiveDirectory__BaseDn: "DC=corp,DC=imip,DC=co,DC=id"
  ActiveDirectory__Username: "<EMAIL>"
  ActiveDirectory__Port: "636"
  ActiveDirectory__UseSsl: "true"
  ActiveDirectory__Enabled: "true"
  ActiveDirectory__AutoLogin: "true"
  ActiveDirectory__DefaultUsername: ""
  ActiveDirectory__WindowsAuthEnabled: "true"
  ActiveDirectory__WindowsAuthServiceUrl: "https://your-windows-auth-service.com"
  ActiveDirectory__WindowsAuthServiceApiKey: ""
  ActiveDirectory__WindowsAuthServiceTimeout: "30"
  Redis__Configuration: "**********:6378,abortConnect=false,connectTimeout=30000,syncTimeout=30000,connectRetry=10,keepAlive=60,allowAdmin=true,responseTimeout=30000"
  ConnectionStrings__Default__CommandTimeout: "60"
  ConnectionStrings__Default__ConnectTimeout: "60"
  ConnectionStrings__Default__ConnectRetryCount: "5"
  ConnectionStrings__Default__ConnectRetryInterval: "10"
  RabbitMQ__Connections__Default__HostName: "**********"
  RabbitMQ__Connections__Default__UserName: "guest"
  RabbitMQ__Connections__Default__Password: "guest"
  RabbitMQ__Connections__Default__Port: "5672"
  RabbitMQ__EventBus__ClientName: "IdentityServer-Dev"
  RabbitMQ__EventBus__ExchangeName: "LogoutEvents"
