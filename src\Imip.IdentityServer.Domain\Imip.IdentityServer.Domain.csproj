﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Imip.IdentityServer</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Imip.IdentityServer.Application.Contracts\Imip.IdentityServer.Application.Contracts.csproj" />
    <ProjectReference Include="..\Imip.IdentityServer.Domain.Shared\Imip.IdentityServer.Domain.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="System.Runtime.InteropServices" Version="4.3.0" />
    <PackageReference Include="System.DirectoryServices.Protocols" Version="9.0.0" />
    <PackageReference Include="Volo.Abp.EventBus.RabbitMQ" Version="9.1.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Volo.Abp.Emailing" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.Caching" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.Identity" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.OpenIddict" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.BackgroundJobs.Domain" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.AuditLogging.Domain" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Domain" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.SettingManagement.Domain" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.BlobStoring.Database.Domain" Version="9.1.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.OpenIddict.Domain" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.Identity.Domain" Version="9.1.0" />
    <PackageReference Include="Volo.Abp.TenantManagement.Domain" Version="9.1.0" />
  </ItemGroup>

</Project>
