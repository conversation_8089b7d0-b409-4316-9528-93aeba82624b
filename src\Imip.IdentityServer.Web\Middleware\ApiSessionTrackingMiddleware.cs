using System;
using System.Linq;
using System.Threading.Tasks;
using System.Security.Claims;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Volo.Abp.Identity;
using Volo.Abp.Security.Claims;
using Volo.Abp.Users;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;
using Imip.IdentityServer.Domain.Services;

namespace Imip.IdentityServer.Web.Middleware
{
    /// <summary>
    /// Middleware to ensure API calls are tracked in the AbpSessions table
    /// </summary>
    public class ApiSessionTrackingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<ApiSessionTrackingMiddleware> _logger;

        public ApiSessionTrackingMiddleware(
            RequestDelegate next,
            ILogger<ApiSessionTrackingMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                // Only process if the user is authenticated
                if (context.User?.Identity?.IsAuthenticated == true)
                {
                    // Check if this is an API call (based on path or accept header)
                    if (IsApiRequest(context))
                    {
                        await ProcessApiSessionAsync(context);
                        await UpdateSessionLastAccessedAsync(context);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in API session tracking middleware");
            }

            // Continue with the request pipeline
            await _next(context);
        }

        private bool IsApiRequest(HttpContext context)
        {
            // Check if the request is for an API endpoint
            // You can customize this logic based on your API routes
            var path = context.Request.Path.ToString().ToLower();

            // Check if path starts with /api or accept header indicates JSON
            return path.StartsWith("/api") ||
                   context.Request.Headers["Accept"].Any(h => h?.Contains("application/json") == true);
        }

        private async Task ProcessApiSessionAsync(HttpContext context)
        {
            var currentUser = context.RequestServices.GetRequiredService<ICurrentUser>();
            if (currentUser?.Id == null)
            {
                return;
            }

            var sessionId = currentUser.FindClaim(AbpClaimTypes.SessionId)?.Value;
            if (string.IsNullOrEmpty(sessionId))
            {
                // Generate a new session ID if one doesn't exist
                sessionId = Guid.NewGuid().ToString("N");
                _logger.LogInformation("Generated new session ID {SessionId} for authenticated API user {UserId}", sessionId, currentUser.Id);

                // Add the session ID to the current principal's claims
                if (context.User.Identity is ClaimsIdentity identity)
                {
                    identity.AddClaim(new Claim(AbpClaimTypes.SessionId, sessionId));
                    _logger.LogDebug("Added session ID claim to the current principal");
                }
            }

            // Get client info
            var clientId = GetClientId(context);
            var device = GetDeviceInfo(context);
            var ipAddress = GetIpAddress(context);
            var deviceInfo = GetDeviceInfoDetail(context);

            try
            {
                // Get the repository for IdentitySession
                var sessionRepository = context.RequestServices.GetRequiredService<IRepository<IdentitySession, Guid>>();

                // Check if session exists
                var session = await sessionRepository.FirstOrDefaultAsync(s => s.SessionId == sessionId);

                if (session == null)
                {
                    // Create a new session
                    _logger.LogInformation("Creating new session for API user with ID {UserId}", currentUser.Id);

                    session = new IdentitySession(
                        Guid.NewGuid(),
                        sessionId,
                        device,
                        deviceInfo,
                        currentUser.Id.Value,
                        null, // tenantId
                        clientId,
                        ipAddress,
                        DateTime.UtcNow, // signedIn
                        DateTime.UtcNow  // lastAccessed
                    );

                    await sessionRepository.InsertAsync(session);
                }
                else
                {
                    // Create a new session with updated values
                    _logger.LogDebug("Updating existing session for API user with ID {UserId}", currentUser.Id);

                    // Create a new session with the same ID but updated values
                    var updatedSession = new IdentitySession(
                        session.Id,
                        session.SessionId,
                        session.Device,
                        deviceInfo,
                        session.UserId,
                        session.TenantId,
                        clientId,
                        ipAddress,
                        session.SignedIn,
                        DateTime.UtcNow // Update last accessed time
                    );

                    await sessionRepository.UpdateAsync(updatedSession);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing API session for user {UserId}", currentUser.Id);
            }
        }

        private static string GetClientId(HttpContext context)
        {
            // Try to get client ID from claims or headers
            return context.User.FindFirst("client_id")?.Value ??
                   context.Request.Headers["X-Client-Id"].FirstOrDefault() ??
                   "api-client";
        }

        private static string GetDeviceInfo(HttpContext context)
        {
            // Determine device type from user agent
            var userAgent = context.Request.Headers.UserAgent.ToString();

            if (string.IsNullOrEmpty(userAgent))
            {
                return "API";
            }

            if (userAgent.Contains("Mobile") || userAgent.Contains("Android") || userAgent.Contains("iPhone"))
            {
                return "Mobile";
            }

            return "Browser";
        }

        private static string GetDeviceInfoDetail(HttpContext context)
        {
            // Return more detailed device info
            var userAgent = context.Request.Headers.UserAgent.ToString();
            return userAgent[..Math.Min(64, userAgent.Length)];
        }

        private static string GetIpAddress(HttpContext context)
        {
            // Get client IP address
            return context.Connection.RemoteIpAddress?.ToString() ??
                   context.Request.Headers["X-Forwarded-For"].FirstOrDefault() ??
                   "unknown";
        }

        private async Task UpdateSessionLastAccessedAsync(HttpContext context)
        {
            try
            {
                var currentUser = context.RequestServices.GetRequiredService<ICurrentUser>();
                if (currentUser?.Id == null)
                {
                    return;
                }

                var sessionId = currentUser.FindClaim(AbpClaimTypes.SessionId)?.Value;
                if (string.IsNullOrEmpty(sessionId))
                {
                    return;
                }

                var sessionManagementService = context.RequestServices.GetRequiredService<SessionManagementService>();
                await sessionManagementService.UpdateSessionLastAccessedAsync(sessionId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating session last accessed time");
            }
        }
    }

    // Extension method to add the middleware to the HTTP request pipeline
    public static class ApiSessionTrackingMiddlewareExtensions
    {
        public static IApplicationBuilder UseApiSessionTracking(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<ApiSessionTrackingMiddleware>();
        }
    }
}
